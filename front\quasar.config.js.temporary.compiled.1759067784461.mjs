/* eslint-disable */
/**
 * THIS FILE IS GENERATED AUTOMATICALLY.
 * 1. DO NOT edit this file directly as it won't do anything.
 * 2. EDIT the original quasar.config file INSTEAD.
 * 3. DO NOT git commit this file. It should be ignored.
 *
 * This file is still here because there was an error in
 * the original quasar.config file and this allows you to
 * investigate the Node.js stack error.
 *
 * After you fix the original file, this file will be
 * deleted automatically.
 **/

var __getOwnPropNames = Object.getOwnPropertyNames;
var __require = /* @__PURE__ */ ((x) => typeof require !== "undefined" ? require : typeof Proxy !== "undefined" ? new Proxy(x, {
  get: (a, b) => (typeof require !== "undefined" ? require : a)[b]
}) : x)(function(x) {
  if (typeof require !== "undefined") return require.apply(this, arguments);
  throw Error('Dynamic require of "' + x + '" is not supported');
});
var __commonJS = (cb, mod) => function __require2() {
  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
};

// package.json
var require_package = __commonJS({
  "package.json"(exports, module) {
    module.exports = {
      name: "lotto",
      version: "1.3.5",
      description: "A lotto app",
      productName: "\u6A02\u900F\u5373\u6642\u5831",
      author: "Yuue <<EMAIL>>",
      private: true,
      scripts: {
        lint: "eslint --ext .js,.ts,.vue ./",
        format: 'prettier --write "**/*.{js,ts,vue,scss,html,md,json}" --ignore-path .gitignore',
        test: 'echo "No test specified" && exit 0',
        dev: "quasar dev -m pwa",
        build: "node scripts/clean-version-cache.js && quasar build -m pwa && node scripts/generate-version.js",
        "generate-version": "node scripts/generate-version.js",
        "clean-version-cache": "node scripts/clean-version-cache.js"
      },
      dependencies: {
        "@quasar/extras": "^1.16.4",
        "@vee-validate/rules": "^4.13.2",
        axios: "^1.2.1",
        bootstrap: "^5.3.3",
        exceljs: "^4.4.0",
        pinia: "^2.0.11",
        "pinia-plugin-persistedstate": "^4.2.0",
        quasar: "^2.16.0",
        "vee-validate": "^4.13.2",
        vue: "^3.4.18",
        "vue-router": "^4.0.12",
        "worker-loader": "^3.0.8",
        xlsx: "^0.18.5"
      },
      devDependencies: {
        "@quasar/app-vite": "^2.4.0",
        "@types/node": "^12.20.21",
        "@typescript-eslint/eslint-plugin": "^5.0.0",
        "@typescript-eslint/parser": "^5.0.0",
        "@typescript-eslint/typescript-estree": "^5.0.0",
        autoprefixer: "^10.4.2",
        eslint: "^8.57.0",
        "eslint-config-prettier": "^8.1.0",
        "eslint-plugin-vue": "^9.0.0",
        prettier: "^2.5.1",
        typescript: "^5.1.6",
        "vite-plugin-checker": "^0.6.4",
        "vue-tsc": "^3.0.8",
        "workbox-build": "^7.1.1",
        "workbox-cacheable-response": "^7.1.0",
        "workbox-core": "^7.1.0",
        "workbox-expiration": "^7.1.0",
        "workbox-precaching": "^7.1.0",
        "workbox-routing": "^7.1.0",
        "workbox-strategies": "^7.1.0"
      },
      engines: {
        node: "^20 || ^18 || ^16",
        npm: ">= 6.13.4",
        yarn: ">= 1.21.1"
      }
    };
  }
});

// scripts/version-utils.js
var require_version_utils = __commonJS({
  "scripts/version-utils.js"(exports, module) {
    var __quasar_inject_dirname__ = "D:\\Workspace\\lottery\\front\\scripts";
    var fs = __require("fs");
    var path = __require("path");
    var VersionUtils = class {
      constructor() {
        this.packageJson = require_package();
        this.versionCacheFile = path.join(__quasar_inject_dirname__, ".version-cache.json");
      }
      /**
       * 獲取或生成版本號
       * @param {boolean} forceNew - 是否強制生成新版本號
       * @returns {string} 版本號
       */
      getVersion(forceNew = false) {
        if (!forceNew) {
          const cachedVersion = this.getCachedVersion();
          if (cachedVersion) {
            console.log("VersionUtils: \u4F7F\u7528\u7DE9\u5B58\u7248\u672C\u865F:", cachedVersion);
            return cachedVersion;
          }
        }
        const timestamp = Date.now();
        const version = `v${this.packageJson.version}_${timestamp.toString().slice(-6)}`;
        this.setCachedVersion(version, timestamp);
        console.log("VersionUtils: \u751F\u6210\u65B0\u7248\u672C\u865F:", version);
        return version;
      }
      /**
       * 獲取完整的版本信息
       * @param {boolean} forceNew - 是否強制生成新版本號
       * @returns {object} 版本信息對象
       */
      getVersionInfo(forceNew = false) {
        const version = this.getVersion(forceNew);
        const timestamp = this.extractTimestamp(version);
        return {
          version,
          baseVersion: `v${this.packageJson.version}`,
          buildTime: new Date(timestamp).toISOString(),
          timestamp
        };
      }
      /**
       * 從版本號中提取時間戳
       * @param {string} version - 版本號
       * @returns {number} 時間戳
       */
      extractTimestamp(version) {
        const match = version.match(/_(\d+)$/);
        if (match) {
          const shortTimestamp = match[1];
          const currentTime = Date.now();
          const currentTimeStr = currentTime.toString();
          const fullTimestamp = currentTimeStr.slice(0, -6) + shortTimestamp;
          return parseInt(fullTimestamp);
        }
        return Date.now();
      }
      /**
       * 從緩存文件讀取版本號
       * @returns {string|null} 緩存的版本號
       */
      getCachedVersion() {
        try {
          if (fs.existsSync(this.versionCacheFile)) {
            const cache = JSON.parse(fs.readFileSync(this.versionCacheFile, "utf8"));
            const now = Date.now();
            const cacheAge = now - cache.createdAt;
            const maxAge = 60 * 60 * 1e3;
            if (cacheAge < maxAge && cache.version && cache.packageVersion === this.packageJson.version) {
              return cache.version;
            }
          }
        } catch (error) {
          console.warn("VersionUtils: \u8B80\u53D6\u7248\u672C\u7DE9\u5B58\u5931\u6557:", error.message);
        }
        return null;
      }
      /**
       * 保存版本號到緩存文件
       * @param {string} version - 版本號
       * @param {number} timestamp - 時間戳
       */
      setCachedVersion(version, timestamp) {
        try {
          const cache = {
            version,
            packageVersion: this.packageJson.version,
            timestamp,
            createdAt: Date.now()
          };
          fs.writeFileSync(this.versionCacheFile, JSON.stringify(cache, null, 2));
          console.log("VersionUtils: \u7248\u672C\u7DE9\u5B58\u5DF2\u4FDD\u5B58");
        } catch (error) {
          console.warn("VersionUtils: \u4FDD\u5B58\u7248\u672C\u7DE9\u5B58\u5931\u6557:", error.message);
        }
      }
      /**
       * 清除版本緩存
       */
      clearCache() {
        try {
          if (fs.existsSync(this.versionCacheFile)) {
            fs.unlinkSync(this.versionCacheFile);
            console.log("VersionUtils: \u7248\u672C\u7DE9\u5B58\u5DF2\u6E05\u9664");
          }
        } catch (error) {
          console.warn("VersionUtils: \u6E05\u9664\u7248\u672C\u7DE9\u5B58\u5931\u6557:", error.message);
        }
      }
      /**
       * 強制生成新版本號並清除緩存
       * @returns {string} 新的版本號
       */
      generateNewVersion() {
        this.clearCache();
        return this.getVersion(true);
      }
    };
    module.exports = VersionUtils;
  }
});

// quasar.config.js
var require_quasar_config = __commonJS({
  "quasar.config.js"(exports, module) {
    var __quasar_inject_dirname__ = "D:\\Workspace\\lottery\\front";
    var configure = __require("quasar/wrappers");
    var path = __require("path");
    var VersionUtils = require_version_utils();
    module.exports = configure(function() {
      const versionUtils = new VersionUtils();
      const envVars = {
        APP_VERSION: versionUtils.getVersion(),
        VITE_WS_URL: process.env.VITE_WS_URL
        // 讓前端代碼自動判斷WebSocket URL
      };
      return {
        // https://v2.quasar.dev/quasar-cli-vite/prefetch-feature
        // preFetch: true,
        // app boot file (/src/boot)
        // --> boot files are part of "main.js"
        // https://v2.quasar.dev/quasar-cli-vite/boot-files
        boot: ["axios"],
        // https://v2.quasar.dev/quasar-cli-vite/quasar-config-js#css
        css: ["app.scss"],
        // https://github.com/quasarframework/quasar/tree/dev/extras
        extras: [
          // 'ionicons-v4',
          // 'mdi-v7',
          // 'fontawesome-v6',
          // 'eva-icons',
          // 'themify',
          // 'line-awesome',
          // 'roboto-font-latin-ext', // this or either 'roboto-font', NEVER both!
          "roboto-font",
          // optional, you are not bound to it
          "material-icons"
          // optional, you are not bound to it
        ],
        // Full list of options: https://v2.quasar.dev/quasar-cli-vite/quasar-config-js#build
        build: {
          target: {
            browser: ["es2019", "edge88", "firefox78", "chrome87", "safari13.1"],
            node: "node20"
          },
          // 啟用worker支持
          workerMode: true,
          chainWebpack(chain) {
            chain.module.rule("worker").test(/\.worker\.ts$/).use("worker-loader").loader("worker-loader").options({
              inline: "no-fallback"
            });
          },
          vueRouterMode: "history",
          // available values: 'hash', 'history'
          // vueRouterBase,
          // vueDevtools,
          // vueOptionsAPI: false,
          // rebuildCache: true, // rebuilds Vite/linter/etc cache on startup
          publicPath: "/",
          // analyze: true,
          env: envVars,
          // rawDefine: {}
          // ignorePublicFolder: true,
          // minify: false,
          // polyfillModulePreload: true,
          // distDir
          extendViteConf(viteConf) {
            Object.assign(viteConf.resolve.alias, {
              "@": path.join(__quasar_inject_dirname__, "./src")
            });
          },
          // viteVuePluginOptions: {},
          vitePlugins: [
            [
              "vite-plugin-checker",
              {
                vueTsc: {
                  tsconfigPath: "tsconfig.vue-tsc.json"
                },
                eslint: {
                  lintCommand: 'eslint "./**/*.{js,ts,mjs,cjs,vue}"'
                }
              },
              { server: false }
            ]
          ]
        },
        // Full list of options: https://v2.quasar.dev/quasar-cli-vite/quasar-config-js#devServer
        devServer: {
          // https: true
          open: true,
          // opens browser window automatically
          openPage: "/login",
          proxy: {
            "/api": {
              target: "http://127.0.0.1:8088",
              ws: true,
              // 啟用WebSocket代理
              changeOrigin: true
            }
          }
        },
        // https://v2.quasar.dev/quasar-cli-vite/quasar-config-js#framework
        framework: {
          config: {},
          // iconSet: 'material-icons', // Quasar icon set
          lang: "zh-tw",
          // Quasar language pack
          // For special cases outside of where the auto-import strategy can have an impact
          // (like functional components as one of the examples),
          // you can manually specify Quasar components/directives to be available everywhere:
          //
          // components: [],
          // directives: [],
          // Quasar plugins
          plugins: ["Dialog", "Notify"]
        },
        // animations: 'all', // --- includes all animations
        // https://v2.quasar.dev/options/animations
        animations: [],
        // https://v2.quasar.dev/quasar-cli-vite/quasar-config-js#sourcefiles
        // sourceFiles: {
        //   rootComponent: 'src/App.vue',
        //   router: 'src/router/index',
        //   store: 'src/store/index',
        //   registerServiceWorker: 'src-pwa/register-service-worker',
        //   serviceWorker: 'src-pwa/custom-service-worker',
        //   pwaManifestFile: 'src-pwa/manifest.json',
        //   electronMain: 'src-electron/electron-main',
        //   electronPreload: 'src-electron/electron-preload'
        // },
        // https://v2.quasar.dev/quasar-cli-vite/developing-ssr/configuring-ssr
        ssr: {
          // ssrPwaHtmlFilename: 'offline.html', // do NOT use index.html as name!
          // will mess up SSR
          // extendSSRWebserverConf (esbuildConf) {},
          // extendPackageJson (json) {},
          pwa: false,
          // manualStoreHydration: true,
          // manualPostHydrationTrigger: true,
          prodPort: 3e3,
          // The default port that the production server should use
          // (gets superseded if process.env.PORT is specified at runtime)
          middlewares: [
            "render"
            // keep this as last one
          ]
        },
        // https://v2.quasar.dev/quasar-cli-vite/developing-pwa/configuring-pwa
        pwa: {
          workboxMode: "injectManifest",
          // 使用自定義 Service Worker
          swFilename: "sw.js",
          manifestFilename: "manifest.json",
          // 確保啟用更新提示
          useCredentialsForManifestTag: false,
          injectPwaMetaTags: true,
          workboxOptions: {
            // 跳過等待，立即啟用新的 Service Worker
            skipWaiting: true,
            clientsClaim: true,
            // 清理舊快取
            cleanupOutdatedCaches: true,
            // 確保 HTML 檔案被正確快取和更新
            navigateFallback: "index.html",
            // 移除前導斜線
            navigateFallbackDenylist: [/sw\.js$/, /workbox-(.)*\.js$/],
            // 配置更積極的緩存策略
            runtimeCaching: [
              {
                urlPattern: /\.worker\.js$/,
                handler: "NetworkFirst",
                options: {
                  cacheName: "worker-cache",
                  networkTimeoutSeconds: 3,
                  cacheableResponse: {
                    statuses: [0, 200]
                  }
                }
              },
              {
                // 對於 HTML 檔案使用 NetworkFirst 策略，優先從網路獲取
                urlPattern: /\.html$/,
                handler: "NetworkFirst",
                options: {
                  cacheName: "html-cache",
                  networkTimeoutSeconds: 2,
                  // 縮短超時時間
                  cacheableResponse: {
                    statuses: [0, 200]
                  }
                }
              },
              {
                // 對於 JS/CSS 檔案使用 NetworkFirst 策略，確保總是獲取最新版本
                urlPattern: /\.(?:js|css)$/,
                handler: "NetworkFirst",
                options: {
                  cacheName: "static-resources",
                  networkTimeoutSeconds: 3,
                  cacheableResponse: {
                    statuses: [0, 200]
                  }
                }
              },
              {
                // 對於圖片和其他靜態資源使用 StaleWhileRevalidate
                urlPattern: /\.(?:png|jpg|jpeg|svg|gif|webp|ico)$/,
                handler: "StaleWhileRevalidate",
                options: {
                  cacheName: "images-cache",
                  cacheableResponse: {
                    statuses: [0, 200]
                  }
                }
              }
            ]
          },
          useFilenameHashes: true
          // extendGenerateSWOptions (cfg) {}
          // extendInjectManifestOptions (cfg) {},
          // extendManifestJson (json) {}
          // extendPWACustomSWConf (esbuildConf) {}
        },
        // Full list of options: https://v2.quasar.dev/quasar-cli-vite/developing-cordova-apps/configuring-cordova
        cordova: {
          // noIosLegacyBuildFlag: true, // uncomment only if you know what you are doing
        },
        // Full list of options: https://v2.quasar.dev/quasar-cli-vite/developing-capacitor-apps/configuring-capacitor
        capacitor: {
          hideSplashscreen: true
        },
        // Full list of options: https://v2.quasar.dev/quasar-cli-vite/developing-electron-apps/configuring-electron
        electron: {
          // extendElectronMainConf (esbuildConf)
          // extendElectronPreloadConf (esbuildConf)
          inspectPort: 5858,
          bundler: "packager",
          // 'packager' or 'builder'
          packager: {
            // https://github.com/electron-userland/electron-packager/blob/master/docs/api.md#options
            // OS X / Mac App Store
            // appBundleId: '',
            // appCategoryType: '',
            // osxSign: '',
            // protocol: 'myapp://path',
            // Windows only
            // win32metadata: { ... }
          },
          builder: {
            // https://www.electron.build/configuration/configuration
            appId: "lotto"
          }
        },
        // Full list of options: https://v2.quasar.dev/quasar-cli-vite/developing-browser-extensions/configuring-bex
        bex: {
          contentScripts: ["my-content-script"]
          // extendBexScriptsConf (esbuildConf) {}
          // extendBexManifestJson (json) {}
        }
      };
    });
  }
});
export default require_quasar_config();
