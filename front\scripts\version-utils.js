const fs = require('fs');
const path = require('path');

/**
 * 版本工具類 - 統一管理版本號生成邏輯
 */
class VersionUtils {
  constructor() {
    this.packageJson = require('../package.json');
    this.versionCacheFile = path.join(__dirname, '.version-cache.json');
  }

  /**
   * 獲取或生成版本號
   * @param {boolean} forceNew - 是否強制生成新版本號
   * @returns {string} 版本號
   */
  getVersion(forceNew = false) {
    if (!forceNew) {
      // 嘗試從緩存文件讀取
      const cachedVersion = this.getCachedVersion();
      if (cachedVersion) {
        console.log('VersionUtils: 使用緩存版本號:', cachedVersion);
        return cachedVersion;
      }
    }

    // 生成新的版本號
    const timestamp = Date.now();
    const version = `v${this.packageJson.version}_${timestamp.toString().slice(-6)}`;

    // 保存到緩存
    this.setCachedVersion(version, timestamp);

    console.log('VersionUtils: 生成新版本號:', version);
    return version;
  }

  /**
   * 獲取完整的版本信息
   * @param {boolean} forceNew - 是否強制生成新版本號
   * @returns {object} 版本信息對象
   */
  getVersionInfo(forceNew = false) {
    const version = this.getVersion(forceNew);
    const timestamp = this.extractTimestamp(version);

    return {
      version: version,
      baseVersion: `v${this.packageJson.version}`,
      buildTime: new Date(timestamp).toISOString(),
      timestamp: timestamp
    };
  }

  /**
   * 從版本號中提取時間戳
   * @param {string} version - 版本號
   * @returns {number} 時間戳
   */
  extractTimestamp(version) {
    const match = version.match(/_(\d+)$/);
    if (match) {
      const shortTimestamp = match[1];
      // 重建完整的時間戳
      const currentTime = Date.now();
      const currentTimeStr = currentTime.toString();
      const fullTimestamp = currentTimeStr.slice(0, -6) + shortTimestamp;
      return parseInt(fullTimestamp);
    }
    return Date.now();
  }

  /**
   * 從緩存文件讀取版本號
   * @returns {string|null} 緩存的版本號
   */
  getCachedVersion() {
    try {
      if (fs.existsSync(this.versionCacheFile)) {
        const cache = JSON.parse(fs.readFileSync(this.versionCacheFile, 'utf8'));

        // 檢查緩存是否過期（1小時）
        const now = Date.now();
        const cacheAge = now - cache.createdAt;
        const maxAge = 60 * 60 * 1000; // 1小時

        if (cacheAge < maxAge && cache.version && cache.packageVersion === this.packageJson.version) {
          return cache.version;
        }
      }
    } catch (error) {
      console.warn('VersionUtils: 讀取版本緩存失敗:', error.message);
    }
    return null;
  }

  /**
   * 保存版本號到緩存文件
   * @param {string} version - 版本號
   * @param {number} timestamp - 時間戳
   */
  setCachedVersion(version, timestamp) {
    try {
      const cache = {
        version: version,
        packageVersion: this.packageJson.version,
        timestamp: timestamp,
        createdAt: Date.now()
      };

      fs.writeFileSync(this.versionCacheFile, JSON.stringify(cache, null, 2));
      console.log('VersionUtils: 版本緩存已保存');
    } catch (error) {
      console.warn('VersionUtils: 保存版本緩存失敗:', error.message);
    }
  }

  /**
   * 清除版本緩存
   */
  clearCache() {
    try {
      if (fs.existsSync(this.versionCacheFile)) {
        fs.unlinkSync(this.versionCacheFile);
        console.log('VersionUtils: 版本緩存已清除');
      }
    } catch (error) {
      console.warn('VersionUtils: 清除版本緩存失敗:', error.message);
    }
  }

  /**
   * 強制生成新版本號並清除緩存
   * @returns {string} 新的版本號
   */
  generateNewVersion() {
    this.clearCache();
    return this.getVersion(true);
  }
}

module.exports = VersionUtils;
